{{ 'quiz-result.css' | asset_url | stylesheet_tag: media: 'screen' }}

<div class="quiz-result-banner {{ section.settings.custom_class }}">
  <div class="container">
    <div class="quiz-result-inner">
      <div class="quiz-result-heading">
        {% if section.settings.title != blank %}
          <div class="quiz-result-title">
            <h2>{{ section.settings.title }}</h2>
          </div>
        {% endif %}
        <div class="quiz-result-subtitle">
          <span class="subtitle-quantity">1 items</span>
          <span class="subtitle-subtotal">$458</span>
          <span class="subtitle-total">$249</span>
        </div>
      </div>
      <div class="quiz-result-wrapper">
        <div class="quiz-result-product">
          <div class="quiz-result-product-list">
            <div class="result-product">
              <div class="result-product-wrap">
                <div class="result-product-image">
                  <img src="https://upstep-custom-orthotics.myshopify.com/cdn/shop/files/d774ad378abc8c46687b93c6.webp">
                </div>
                <div class="result-product-info">
                  <div class="result-product-name">
                    <h3>
                      <a href="https://upstep-custom-orthotics.myshopify.com/products/on-my-feet-all-day-custom-orthotics">
                        On My Feet All Day – Custom Orthotics
                      </a>
                    </h3>
                  </div>
                  <div class="result-product-price">
                    <span class="product-old-price">$458</span>
                    <span class="product-new-price">SALE $249</span>
                  </div>
                  <div class="result-product-quantity">
                    <div class="result-product-remove">Remove</div>
                    <div class="product-quantity-count">
                      <button type="button" class="quantity-btn_plus">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                          <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                        </svg>
                      </button>
                      <div class="quantity-value_text"><span class="product-quantity">1</span> <span>Pair</span></div>
                      <button type="button" class="quantity-btn_minus">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                          <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="result-product-content">
                  <h4 class="product-content-title accordion-toggle">Your Upstep Info</h4>
                  <div class="product-content-list accordion-content">
                    <p><strong>What shoe size do you wear?:</strong> 7</p>
                    <p><strong>Pains:</strong> Right and Left The arch</p>
                    <p><strong>Diagnosis:</strong> Right and Left Plantar Fasciitis</p>
                    <p><strong>Sport:</strong> Basketball</p>
                    <p><strong>Basketball Shoes:</strong> Low/Mid Top</p>
                    <p><strong>Gender:</strong> Man</p>
                  </div>
                </div>
              </div>
              <div class="result-product-removing_message">
                <div class="result-product-message_wrapper">
                  <div class="result-product-form_block">
                    <div class="result-product-message">Remove from cart?</div>
                    <div class="result-product-buttons_wrapper">
                      <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                      <button type="button" class="btn btn_gray no-remove-product">No</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="result-suggest-product">
            {% if section.settings.suggest_title != blank %}
              <div class="result-suggest-title">
                <h3>{{ section.settings.suggest_title }}</h3>
              </div>
            {% endif %}
            <div class="result-suggest-list">
              {% for product in section.settings.product_list %}
                <div class="suggest-product-block">
                  <div class="suggest-product-image">
                    <img src="{{ product.featured_image | img_url: '300x' }}" alt="{{ product.title }}">
                  </div>
                  <div class="suggest-product-info">
                    <div class="suggest-product-content">
                      <div class="suggest-product-title">
                        <h5>{{ product.title }}</h5>
                      </div>
                      <div class="suggest-product-price">
                        <span class="suggest-product-price-new">
                          {{ product.price | money }}
                        </span>
                        {% if product.compare_at_price > product.price %}
                          <span class="suggest-product-price-old">
                            {{ product.compare_at_price | money }}
                          </span>
                        {% endif %}
                      </div>
                    </div>
                    <div class="suggest-product-add">
                      <button class="suggest-product-add-btn" type="button" data-suggest-pro-id="{{ product.id }}" data-suggest-pro-var-id="{{ product.variants.first.id }}" data-suggest-pro-price="{{ product.price }}" data-suggest-pro-compare-price="{{ product.compare_at_price }}">Add</button>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
        <div class="quiz-result-cart">
          <div class="quiz-result-cart-title">Summary</div>
          <div class="quiz-result-cart-info">
            <div class="result-shipping">
              <div class="result-shipping_img">
                <img
                  src="https://dcpsvjmu5hpqr.cloudfront.net/images/2021-11/638755d51c7bb99069d28f86.svg"
                  alt="Free &amp; fast shipping.svg"
                >
              </div>
              <div class="result-shipping_title">Free Shipping</div>
            </div>
            <div class="result-cart-top">
              <div class="result-cart-subtotal result-cart-text">
                <div class="cart-subtotal-label">Subtotal</div>
                <div class="cart-subtotal-price">$458</div>
              </div>
              <div class="result-cart-shipping result-cart-text">
                <div class="cart-shipping-label">Shipping</div>
                <div class="cart-shipping-price">FREE</div>
              </div>
              <div class="result-cart-discounts result-cart-text">
                <div class="cart-discounts-label">Discounts</div>
                <div class="cart-discounts-price">- $209</div>
              </div>
            </div>
            <div class="result-cart-bottom">
              <div class="result-cart-total">
                <div class="cart-discounts-label">Total:</div>
                <div class="cart-discounts-price">$249</div>
              </div>
              <div class="result-cart-submit">
                <button class="overlay-buy_button result-cart-btn" type="submit">Secure Checkout</button>
              </div>
              <ul class="result-cart-payment">
                {%- for payment_method in shop.enabled_payment_types -%}
                  <li>{{ payment_method | payment_type_svg_tag }}</li>
                {%- endfor -%}
              </ul>
              <div class="result-cart-free-shipping">
                <svg xmlns="http://www.w3.org/2000/svg" width="11" height="12" fill="none">
                  <path stroke="#53BA65" stroke-linecap="round" stroke-width="2" d="m10 2-5.143 8M1 5.2 4.857 10"/>
                </svg>
                <p>Your order is eligible for free express shipping</p>
              </div>
            </div>
          </div>
          <ul class="result-cart-payment-mobile">
            {%- for payment_method in shop.enabled_payment_types -%}
              <li>{{ payment_method | payment_type_svg_tag }}</li>
            {%- endfor -%}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="quiz-result-fixed_button">
    <div class="quiz-result-button_wrapper">
      <div class="quiz-result-total_row">
        <div class="result-fixed-total-label">Total</div>
        <div class="result-fixed-total_price">
          <div class="result-fixed-price_old">$458</div>
          <div class="result-fixed-price_new">$249</div>
        </div>
      </div>
      <button class="result-fixed-btn" type="button">Secure Checkout</button>
      <div class="result-fixed-bottom-line">180-day money-back guarantee</div>
    </div>
  </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script>
$(document).ready(function () {
  // Cart synchronization functions
  let cartSyncEnabled = true;
  let isUpdatingFromCart = false;

  // Load existing cart items on page load
  function loadCartItems() {
    if (!cartSyncEnabled) return;

    fetch('/cart.js')
      .then(response => response.json())
      .then(cartData => {
        console.log('Cart data loaded:', cartData); // Debug log
        if (cartData.items && cartData.items.length > 0) {
          syncCartToResultPage(cartData.items);
        }
      })
      .catch(error => {
        console.log('Error loading cart items:', error);
      });
  }

  // Sync cart items to result page
  function syncCartToResultPage(cartItems) {
    console.log('Syncing cart items to result page:', cartItems); // Debug log
    isUpdatingFromCart = true;

    // Clear existing products except quiz result
    $('.quiz-result-product-list .result-product').each(function() {
      if (!$(this).hasClass('quiz-result-product')) {
        $(this).remove();
      }
    });

    // Add cart items to result page
    cartItems.forEach(function(item) {
      addCartItemToResultPage(item);
    });

    updateCartSummary();
    isUpdatingFromCart = false;
  }

  // Add cart item to result page
  function addCartItemToResultPage(cartItem) {
    let existingProduct = $(`.result-product[data-var-id="${cartItem.variant_id}"]`);

    if (existingProduct.length > 0) {
      // Update quantity if product already exists
      console.log('Updating existing product quantity:', cartItem.variant_id, cartItem.quantity);
      existingProduct.find('.product-quantity').text(cartItem.quantity);
    } else {
      // Add new product
      console.log('Adding new product to result page:', cartItem.variant_id);
      let productHtml = createProductHtml(cartItem);
      $('.quiz-result-product-list').append(productHtml);
    }
  }

  // Create product HTML from cart item
  function createProductHtml(cartItem) {
    console.log('Creating HTML for cart item:', cartItem); // Debug log

    let price = cartItem.price;
    let comparePrice = cartItem.original_price || cartItem.price;
    let priceFormatted = "SALE " + USFormatMoney(price);
    let comparePriceFormatted = USFormatMoney(comparePrice);

    // Handle missing image - cart.js returns image URL directly
    let imageUrl = cartItem.image || cartItem.featured_image || '';
    if (imageUrl && !imageUrl.startsWith('http')) {
      imageUrl = 'https:' + imageUrl;
    }

    return `
      <div class="result-product"
           data-pro-id="${cartItem.product_id}"
           data-var-id="${cartItem.variant_id}"
           data-price="${price}"
           data-compare-price="${comparePrice}">
        <div class="result-product-wrap">
          <div class="result-product-image">
            <img src="${imageUrl}" alt="${cartItem.product_title || 'Product Image'}">
          </div>
          <div class="result-product-info">
            <div class="result-product-name">
              <h3>
                <a href="${cartItem.url || '#'}">${cartItem.product_title || 'Product'}</a>
              </h3>
            </div>
            <div class="result-product-price">
              <span class="product-old-price">${comparePriceFormatted}</span>
              <span class="product-new-price">${priceFormatted}</span>
            </div>
            <div class="result-product-quantity">
              <div class="result-product-remove">Remove</div>
              <div class="product-quantity-count">
                <button type="button" class="quantity-btn_plus">
                  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                    <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                  </svg>
                </button>
                <div class="quantity-value_text">
                  <span class="product-quantity">${cartItem.quantity}</span> <span>Pair</span>
                </div>
                <button type="button" class="quantity-btn_minus">
                   <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                    </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="result-product-removing_message">
          <div class="result-product-message_wrapper">
            <div class="result-product-form_block">
              <div class="result-product-message">Remove from cart?</div>
              <div class="result-product-buttons_wrapper">
                <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                <button type="button" class="btn btn_gray no-remove-product">No</button>
              </div>
            </div>
          </div>
        </div>
      </div>`;
  }

  // Sync quantity change to cart using the same approach as the theme
  function syncQuantityToCart(variantId, newQuantity) {
    if (isUpdatingFromCart) return;

    console.log('Syncing quantity to cart:', variantId, newQuantity); // Debug log

    // Use JSON approach like the theme does for cart updates
    let config = {
      method: 'POST',
      body: JSON.stringify({
        'updates': {
          [variantId]: newQuantity
        },
        'sections': 'side-cart'
      }),
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json',
        'Accept': 'application/javascript'
      }
    };

    fetch('/cart/update.js', config)
    .then(response => response.json())
    .then(data => {
      console.log('Cart update response:', data); // Debug log
      if (data.status) {
        console.error('Error updating cart:', data.description);
        alert('Error updating cart: ' + data.description);
      } else {
        console.log('Cart updated successfully');
        // Update cart count in header if exists
        if (typeof ajaxCart !== 'undefined' && ajaxCart.load) {
          ajaxCart.load(false, false, false, true);
        }
      }
    })
    .catch(error => {
      console.error('Error syncing quantity to cart:', error);
      alert('Error syncing quantity to cart');
    });
  }

  // Remove item from cart using the same approach as the theme
  function removeFromCart(variantId) {
    if (isUpdatingFromCart) return;

    console.log('Removing from cart:', variantId); // Debug log

    // Use JSON approach like the theme does for cart updates
    let config = {
      method: 'POST',
      body: JSON.stringify({
        'updates': {
          [variantId]: 0
        },
        'sections': 'side-cart'
      }),
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json',
        'Accept': 'application/javascript'
      }
    };

    fetch('/cart/update.js', config)
    .then(response => response.json())
    .then(data => {
      console.log('Cart remove response:', data); // Debug log
      if (data.status) {
        console.error('Error removing from cart:', data.description);
        alert('Error removing from cart: ' + data.description);
      } else {
        console.log('Item removed from cart successfully');
        // Update cart count in header if exists
        if (typeof ajaxCart !== 'undefined' && ajaxCart.load) {
          ajaxCart.load(false, false, false, true);
        }
      }
    })
    .catch(error => {
      console.error('Error removing from cart:', error);
      alert('Error removing from cart');
    });
  }

  // Load cart items on page load
  loadCartItems();

  function initAccordion() {
    if ($(window).width() <= 767) {
      $(document).on("click", ".accordion-toggle", function () {
          $(this).toggleClass("active");
          $(this).next(".accordion-content").slideToggle();
        });
    } else {
      $(".accordion-content").show();
      $(".accordion-toggle").removeClass("active");
    }
  }

  initAccordion();
  $(window).resize(function () {
    initAccordion();
  });

  $(document).on("click", ".quantity-btn_plus", function () {
    let $qty = $(this)
      .siblings(".quantity-value_text")
      .find(".product-quantity");
    let currentValue = parseInt($qty.text());
    let newValue = currentValue + 1;
    $qty.text(newValue);

    // Sync with cart
    let $product = $(this).closest(".result-product");
    let variantId = $product.data("var-id");
    if (variantId) {
      syncQuantityToCart(variantId, newValue);
    }

    updateCartSummary();
  });

  $(document).on("click", ".quantity-btn_minus", function () {
    let $qty = $(this)
      .siblings(".quantity-value_text")
      .find(".product-quantity");
    let currentValue = parseInt($qty.text());

    if (currentValue > 1) {
      let newValue = currentValue - 1;
      $qty.text(newValue);

      // Sync with cart
      let $product = $(this).closest(".result-product");
      let variantId = $product.data("var-id");
      if (variantId) {
        syncQuantityToCart(variantId, newValue);
      }
    } else {
      let $product = $(this).closest(".result-product");

      if ($(window).width() <= 767) {
        $product.find(".result-product-removing_message")
                .addClass("show-remove-message")
                .fadeIn(200);
      } else {
        return;
      }
    }
    updateCartSummary();
  });

  $(document).on("click", ".result-product-remove", function () {
    let $product = $(this).closest(".result-product");
    $product.find(".result-product-removing_message").addClass("show-remove-message").fadeIn(200);
  });

  $(document).on("click", ".result-product-removing_message .no-remove-product", function () {
    let $product = $(this).closest(".result-product");
    $product.find(".result-product-removing_message").removeClass("show-remove-message").fadeOut(200);
  });

  $(document).on("click", ".result-product-removing_message .yes-remove-product", function () {
    let $product = $(this).closest(".result-product");
    let variantId = $product.data("var-id");

    // Remove from cart first
    if (variantId) {
      removeFromCart(variantId);
    }

    $product.remove();
    updateCartSummary();
  });
});

let urlParams = new URLSearchParams(window.location.search);
let code = urlParams.get("code");
if (code) {
  $.ajax({
    url: "https://api.quizell.com/api/leadData",
    type: "POST",
    contentType: "application/json",
    data: JSON.stringify({
      code: code,
      question_answers: true,
      product_fields: [
        "id",
        "title",
        "external_id",
        "image",
        "price",
        "compare_at_price",
        "detail_link",
        "variant_id",
      ],
    }),
    success: function (response) {
      if (response.data && response.status == "success") {
        var product_details = response.data.products[0];

        var pro_title = product_details.title;
        var pro_img = product_details.image;
        var actual_price = product_details.price * 100;
        var actual_compare_price = product_details.compare_at_price * 100;
        var pro_price = "SALE " + USFormatMoney(actual_price);
        var pro_compare_price = USFormatMoney(actual_compare_price);
        var pro_link = product_details.detail_link;

        console.log('Full response data:', response.data); // Debug log
        console.log('Question answers:', response.data.questionAnswers); // Debug log

        var pains = response.data.questionAnswers[1].selectedOption;
        console.log('Pains data:', pains, 'Type:', typeof pains, 'Is array:', Array.isArray(pains)); // Debug log

        var diagnosis = response.data.questionAnswers[2].selectedOption.value;
        var upstep_plan = response.data.questionAnswers[3].selectedOption.value;
        var sport = response.data.questionAnswers[4].selectedOption.value;
        var sport_preffer = response.data.questionAnswers[5].selectedOption.value;
        var gender = response.data.questionAnswers[6].selectedOption.value;
        var shoe_size = response.data.questionAnswers[7].selectedOption.value;

        let titles = [];
        let pain_str = '';

        try {
          // More robust handling of pains data
          if (Array.isArray(pains)) {
            console.log('Processing pains as array');
            pains.forEach(function (pain, index) {
              if (pain && pain.value) {
                titles.push(pain.value.trim());
              }
            });
          } else if (pains && typeof pains === 'object' && pains.value) {
            console.log('Processing pains as single object');
            titles.push(pains.value.trim());
          } else if (typeof pains === 'string') {
            console.log('Processing pains as string');
            titles.push(pains.trim());
          } else {
            console.log('Pains data format not recognized, using fallback');
            titles.push('Not specified');
          }

          pain_str = titles.join(", ");
          console.log('Final pain_str:', pain_str);
        } catch (error) {
          console.error('Error processing pains data:', error);
          pain_str = 'Error processing pain data';
        }

        // Add quiz result product to cart using FormData like the theme does
        console.log('Adding quiz result to cart, variant ID:', product_details.variant_id);

        let formData = new FormData();
        formData.append('id', product_details.variant_id);
        formData.append('quantity', 1);
        formData.append('sections', 'side-cart');

        console.log('FormData contents:', {
          id: product_details.variant_id,
          quantity: 1,
          sections: 'side-cart'
        });

        fetch('/cart/add.js', {
          method: 'POST',
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/javascript'
          },
          body: formData
        })
        .then(response => {
          console.log('Cart add response status:', response.status);
          return response.json();
        })
        .then(data => {
          console.log('Quiz result add to cart response:', data);
          if (data.status) {
            console.error('Error adding quiz result to cart:', data.description);
            alert('Error adding quiz result to cart: ' + data.description);
          } else {
            console.log('Quiz result added to cart successfully');
            // Update cart count in header if exists
            if (typeof ajaxCart !== 'undefined' && ajaxCart.load) {
              console.log('Updating cart via ajaxCart.load');
              ajaxCart.load(false, false, false, true);
            } else {
              console.log('ajaxCart not available');
            }
          }
        })
        .catch(error => {
          console.error('Error adding quiz result to cart:', error);
          alert('Error adding quiz result to cart');
        });

        var pro_html = `<div class="result-product quiz-result-product" data-pro-id="${product_details.external_id}" data-var-id="${product_details.variant_id}" data-price="${actual_price}" data-compare-price="${actual_compare_price}">
                          <div class="result-product-wrap">
                            <div class="result-product-image">
                              <img src="${pro_img}">
                            </div>
                            <div class="result-product-info">
                              <div class="result-product-name">
                                <h3>
                                  <a href="${pro_link}">
                                    ${pro_title}
                                  </a>
                                </h3>
                              </div>
                              <div class="result-product-price">
                                <span class="product-old-price">${pro_compare_price}</span>
                                <span class="product-new-price">${pro_price}</span>
                              </div>
                              <div class="result-product-quantity">
                                <div class="result-product-remove">Remove</div>
                                <div class="product-quantity-count">
                                  <button type="button" class="quantity-btn_plus">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                                    </svg>
                                  </button>
                                  <div class="quantity-value_text"><span class="product-quantity">1</span> <span>Pair</span></div>
                                  <button type="button" class="quantity-btn_minus">
                                     <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            </div>
                            <div class="result-product-content">
                              <h4 class="product-content-title accordion-toggle">Your Upstep Info</h4>
                              <div class="product-content-list accordion-content">
                                <p><strong>What shoe size do you wear?:</strong> ${shoe_size}</p>
                                <p><strong>Pains:</strong> ${pain_str}</p>
                                <p><strong>Diagnosis:</strong> ${diagnosis}</p>
                                <p><strong>${upstep_plan}:</strong> ${sport}</p>
                                <p><strong>${sport} Shoes:</strong> ${sport_preffer}</p>
                                <p><strong>Gender:</strong> ${gender}</p>
                              </div>
                            </div>
                          </div>
                          <div class="result-product-removing_message">
                            <div class="result-product-message_wrapper">
                              <div class="result-product-form_block">
                                <div class="result-product-message">Remove from cart?</div>
                                <div class="result-product-buttons_wrapper">
                                  <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                                  <button type="button" class="btn btn_gray no-remove-product">No</button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>`;
        $(".quiz-result-product-list").html(pro_html);
        updateCartSummary();
      }
    },
  });
}

$(document).on("click", ".suggest-product-add-btn", function () {
  let $btn = $(this);
  let pro_id = $btn.data("suggest-pro-id");
  let var_id = $btn.data("suggest-pro-var-id");
  let pro_price = $btn.data("suggest-pro-price");
  let pro_compare_price = $btn.data("suggest-pro-compare-price");

  let pro_title = $btn.closest(".suggest-product-block").find("h5").text();
  let pro_img = $btn.closest(".suggest-product-block").find("img").attr("src");

  let pro_price_formatted = "SALE " + USFormatMoney(pro_price);
  let pro_compare_price_formatted = USFormatMoney(pro_compare_price);

  // Add to cart first using FormData like the theme does
  console.log('Adding suggest product to cart, variant ID:', var_id);

  let formData = new FormData();
  formData.append('id', var_id);
  formData.append('quantity', 1);
  formData.append('sections', 'side-cart');

  console.log('FormData contents:', {
    id: var_id,
    quantity: 1,
    sections: 'side-cart'
  });

  fetch('/cart/add.js', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'Accept': 'application/javascript'
    },
    body: formData
  })
  .then(response => {
    console.log('Cart add response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Suggest product add to cart response:', data);
    if (data.status) {
      console.error('Error adding to cart:', data.description);
      alert('Error adding product to cart: ' + data.description);
      return;
    }

    console.log('Suggest product added to cart successfully');
    // Update cart count in header if exists
    if (typeof ajaxCart !== 'undefined' && ajaxCart.load) {
      console.log('Updating cart via ajaxCart.load');
      ajaxCart.load(false, false, false, true);
    } else {
      console.log('ajaxCart not available');
    }
  })
  .catch(error => {
    console.error('Error adding to cart:', error);
    alert('Error adding product to cart');
    return;
  });

  // Check if product already exists
  let existingProduct = $(`.result-product[data-var-id="${var_id}"]`);
  if (existingProduct.length > 0) {
    // Update quantity if product already exists
    let currentQty = parseInt(existingProduct.find('.product-quantity').text());
    existingProduct.find('.product-quantity').text(currentQty + 1);
  } else {
    // Add new product to display
    let pro_html = `
      <div class="result-product"
           data-pro-id="${pro_id}"
           data-var-id="${var_id}"
           data-price="${pro_price}"
           data-compare-price="${pro_compare_price}">
        <div class="result-product-wrap">
          <div class="result-product-image">
            <img src="${pro_img}">
          </div>
          <div class="result-product-info">
            <div class="result-product-name">
              <h3>${pro_title}</h3>
            </div>
            <div class="result-product-price">
              <span class="product-old-price">${pro_compare_price_formatted}</span>
              <span class="product-new-price">${pro_price_formatted}</span>
            </div>
            <div class="result-product-quantity">
              <div class="result-product-remove">Remove</div>
              <div class="product-quantity-count">
                <button type="button" class="quantity-btn_plus">
                  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                    <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                  </svg>
                </button>
                <div class="quantity-value_text">
                  <span class="product-quantity">1</span> <span>Pair</span>
                </div>
                <button type="button" class="quantity-btn_minus">
                   <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                    </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="result-product-removing_message">
          <div class="result-product-message_wrapper">
            <div class="result-product-form_block">
              <div class="result-product-message">Remove from cart?</div>
              <div class="result-product-buttons_wrapper">
                <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                <button type="button" class="btn btn_gray no-remove-product">No</button>
              </div>
            </div>
          </div>
        </div>
      </div>`;

    $(".quiz-result-product-list").append(pro_html);
  }
  updateCartSummary();
});

function updateCartSummary() {
  let totalQty = $(".result-product-wrap").length;
  let subtotal = 0;
  let total = 0;

  $(".result-product").each(function () {
    let qty = parseInt($(this).find(".product-quantity").text());
    let oldPrice = parseFloat(
      $(this).find(".product-old-price").text().replace(/[^0-9.-]+/g, "")
    );
    let newPrice = parseFloat(
      $(this).find(".product-new-price").text().replace(/[^0-9.-]+/g, "")
    );

    if (!isNaN(oldPrice)) subtotal += oldPrice * qty;
    if (!isNaN(newPrice)) total += newPrice * qty;
  });

  let discount = subtotal - total;
  let subtotalCents = Math.round(subtotal * 100);
  let totalCents = Math.round(total * 100);
  let discountCents = Math.round(discount * 100);

  $(".subtitle-quantity").text(totalQty + " items");
  $(".subtitle-subtotal").text(USFormatMoney(subtotalCents));
  $(".subtitle-total").text(USFormatMoney(totalCents));

  $(".cart-subtotal-price").text(USFormatMoney(subtotalCents));
  $(".result-cart-discounts .cart-discounts-price").text("- " + USFormatMoney(discountCents));
  $(".result-cart-total .cart-discounts-price").text(USFormatMoney(totalCents));

  $(".result-fixed-price_old").text(USFormatMoney(subtotalCents));
  $(".result-fixed-price_new").text(USFormatMoney(totalCents));
}

$(document).on("click", ".overlay-buy_button, .result-cart-btn, .result-fixed-btn", function (e) {
  e.preventDefault();

  // Since products are already synced with cart, just redirect to cart
  window.location.href = '/cart';
});


var USFormatMoney = function (cents) {
    var format = "{{ shop.money_format }}";
    if (typeof cents == "undefined" || cents == null) {
        return ""
    }
    if (typeof cents == "string" && cents.length == 0) {
        return ""
    }

    var value = "",
            placeholderRegex = /\{\{\s*(\w+)\s*\}\}/,
            formatString = format || this.money_format;
    if (typeof cents == "string") {
        cents = cents.replace(".", "")
    }

    function defaultOption(opt, def) {
        return typeof opt == "undefined" ? def : opt
    }

    function formatWithDelimiters(number, precision, thousands, decimal) {
        precision = defaultOption(precision, 2);
        thousands = defaultOption(thousands, ",");
        decimal = defaultOption(decimal, ".");
        if (isNaN(number) || number == null) {
            return 0
        }
        number = (number / 100).toFixed(precision);
        var parts = number.split("."),
                dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1" + thousands),
                cents = parts[1] ? decimal + parts[1] : "";
        return dollars + cents
    }
    switch (formatString.match(placeholderRegex)[1]) {
        case "amount":
            value = formatWithDelimiters(cents, 2);
            break;
        case "amount_no_decimals":
            value = formatWithDelimiters(cents, 0);
            break;
        case "amount_with_comma_separator":
            value = formatWithDelimiters(cents, 2, ".", ",");
            break;
        case"amount_with_space_separator":
            value = formatWithDelimiters(cents, 2, " ", ",");
            break;
        case"amount_with_period_and_space_separator":
            value = formatWithDelimiters(cents, 2, " ", ".");
            break;
        case "amount_no_decimals_with_comma_separator":
            value = formatWithDelimiters(cents, 0, ".", ",");
            break;
        case"amount_no_decimals_with_space_separator":
            value = formatWithDelimiters(cents, 0, ".", "");
            break;
        case"amount_with_space_separator":
            value = formatWithDelimiters(cents, 2, ",", "");
            break;
        case"amount_with_apostrophe_separator":
            value = formatWithDelimiters(cents, 2, "'", ".")
            break;
    }
    return formatString.replace(placeholderRegex, value)
}
</script>

{% schema %}
{
  "name": "Quiz result banner",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "custom_class",
      "label": "Custom class"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "inline_richtext",
      "id": "suggest_title",
      "label": "Suggest title"
    },
    {
      "type": "product_list",
      "id": "product_list",
      "label": "Select product"
    }
  ],
  "presets": [
    {
      "name": "Quiz result banner"
    }
  ]
}
{% endschema %}
